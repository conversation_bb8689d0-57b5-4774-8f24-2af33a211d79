import { DBOS } from "@dbos-inc/dbos-sdk";
import { app } from './config/express';
import { setupRoutes } from './routes';

// Setup API routes
setupRoutes(app);

// Main function
async function main() {
  // Configuration is loaded from dbos-config.yaml
  console.log(`🏛️  Regulatory Compliance System starting...`);
  console.log(`📊 Compliance checking, KYC processing, and regulatory monitoring active`);

  await DBOS.launch({ expressApp: app });
}

main().catch(console.log);